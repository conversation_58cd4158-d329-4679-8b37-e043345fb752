import React, { useEffect, useRef, useState, useCallback } from 'react';
import { gsap } from 'gsap';
// 🌟 CISCO: ÉTOILES UNIFIÉES - SYSTÈME COMPLET
import UnifiedStars from './UnifiedStars';

interface ModeLeverSoleilProps {
  isActive: boolean;
  intensity?: number; // 0-1, progression du lever de soleil
  autoStart?: boolean; // Démarrage automatique de la séquence
  onProgressionStart?: () => void; // Callback quand la progression démarre
  timerDuration?: number; // Durée du temporisateur en secondes (défaut: 60s)
  onTimerStart?: () => void; // Callback externe pour démarrer la progression
  showDevModal?: boolean; // Affiche la modale d'infos dev
  onCloseDevModal?: () => void; // Ferme la modale d'infos dev
}

// 🎯 CISCO: Interface pour contrôle externe
export interface ModeLeverSoleilRef {
  startProgression: () => void;
  stopProgression: () => void;
  getCurrentPhase: () => string;
}

/**
 * 🌅 MODULE LEVER DE SOLEIL - TOUT EN UN
 *
 * CISCO INSTRUCTIONS: Ce module contient TOUT pour le lever du soleil
 * ✅ DÉMARRAGE EN MODE NUIT (tout sombre, étoiles visibles)
 * ✅ SONS NUIT → LEVER DE SOLEIL (temporisation)
 * ✅ Éclairage global spécifique au lever
 * ✅ Position du soleil qui se lève
 * ✅ Quelques étoiles qui disparaissent progressivement
 * ✅ Éclairage du paysage qui s'éclaircit
 * ✅ Dégradé de couleurs aube (monte du bas vers le haut)
 * ✅ TEMPORISATION AUTOMATIQUE progressive
 */
const ModeLeverSoleil: React.FC<ModeLeverSoleilProps> = ({
  isActive,
  intensity = 0.0, // 🔧 CISCO: Démarrage à 0 (nuit complète)
  autoStart = false, // 🔧 CISCO: Pas de démarrage automatique par défaut
  onProgressionStart,
  timerDuration = 12, // 12s POUR TEST (proportionnel à lune 40s)
  showDevModal = false,
  onCloseDevModal
}) => {
  // Timer dynamique : 10s en mode dev, sinon valeur reçue ou 120s par défaut
  // Timer fixe : 120s
  const effectiveTimerDuration = timerDuration;
  // 🎯 RÉFÉRENCES POUR LES ÉLÉMENTS
  const containerRef = useRef<HTMLDivElement>(null);
  const sunRef = useRef<HTMLDivElement>(null);
  const moonRef = useRef<HTMLImageElement>(null); // 🌙 Ajout lune (corrigé pour <img>)
  const starsContainerRef = useRef<HTMLDivElement>(null);
  const globalLightRef = useRef<HTMLDivElement>(null);
  const landscapeRef = useRef<HTMLDivElement>(null);
  const timelineRef = useRef<gsap.core.Timeline | null>(null);
  // 🎵 Références audio multiples pour mixer les ambiances
  const nightAudioRefs = useRef<HTMLAudioElement[]>([]);
  const sunriseAudioRefs = useRef<HTMLAudioElement[]>([]);
  const sunriseStartedRef = useRef(false);
  const moonIntervalRef = useRef<NodeJS.Timeout | null>(null); // 🌙 CISCO: Référence pour éviter les conflits

  // 🌅 ÉTAT INTERNE POUR LA PROGRESSION AUTOMATIQUE
  const [currentIntensity, setCurrentIntensity] = useState(0.0); // Démarrage nuit complète
  // 🌙 Intensité indépendante pour la lune
  const [moonIntensity, setMoonIntensity] = useState(0.0);
  const [isProgressing, setIsProgressing] = useState(false);
  // Indique explicitement la fin de course de la lune
  const [isMoonFinished, setIsMoonFinished] = useState(false);
  // ⏱️ Durée du cycle de la lune (référence) :
  // moonSpeedFactor = 400 secondes (très lent, peut être ajusté)
  // Pour synchronisation, on attend que moonIntensity atteigne 1.0
  const [currentPhase, setCurrentPhase] = useState<'nuit' | 'transition' | 'lever'>('nuit');

  // 🌅 CISCO: DÉGRADÉ SIMPLE - SEULEMENT 2 COULEURS PROGRESSIVES
  const SIMPLE_COLORS = {
    // Début : Nuit profonde
    NIGHT_START: '#001540',      // Bleu nuit profond
    // Fin : Jour clair
    DAY_END: '#87CEEB'           // Bleu ciel jour
  };

  // 🎵 GESTION AUDIO SELON CISCO INSTRUCTIONS
  const AUDIO_CONFIG = {
    night: {
      files: [
        '/sounds/nuit-profonde/night-atmosphere-with-crickets-374652.mp3',
        '/sounds/nuit-profonde/hibou-molkom.mp3'
      ],
      volumes: [0.6, 0.35]
    },
    sunrise: {
      files: [
        '/sounds/aube/village_morning_birds_roosters.mp3',
        '/sounds/lever-soleil/blackbird.mp3'
      ],
      volumes: [0.3, 0.4]
    },
    fadeOutDuration: 5000,  // 5 secondes de fade out
    fadeInDuration: 3000    // 3 secondes de fade in
  } as const;

  // 🌟 CISCO: ÉTOILES DÉPLACÉES DANS SimpleStars.tsx

  // 🎵 GESTION AUDIO PROGRESSIVE
  const playNightSound = useCallback(() => {
    // Stop sunrise audios
    sunriseAudioRefs.current.forEach(a => { try { a.pause(); } catch {} });
    sunriseAudioRefs.current = [];
    sunriseStartedRef.current = false;

    // Stop existing night audios
    nightAudioRefs.current.forEach(a => { try { a.pause(); } catch {} });
    nightAudioRefs.current = [];

    AUDIO_CONFIG.night.files.forEach((file, idx) => {
      const a = new Audio(file);
      a.loop = true;
      a.volume = AUDIO_CONFIG.night.volumes[idx] ?? 0.5;
      a.play().catch(console.warn);
      nightAudioRefs.current.push(a);
    });
    console.log('🌙 Sons de nuit démarrés (criquets + hibou)');
  }, []);

  const transitionToSunriseSound = useCallback(() => {
    if (sunriseStartedRef.current) return;
    sunriseStartedRef.current = true;

    // Fade out all night audios
    let completed = 0;
    const total = nightAudioRefs.current.length;
    const stopAndStart = () => {
      // Stop night audios and start sunrise
      nightAudioRefs.current.forEach(a => { try { a.pause(); } catch {} });
      nightAudioRefs.current = [];
      // Start sunrise audios
      sunriseAudioRefs.current.forEach(a => { try { a.pause(); } catch {} });
      sunriseAudioRefs.current = [];
      AUDIO_CONFIG.sunrise.files.forEach((file, idx) => {
        const a = new Audio(file);
        a.loop = true;
        a.volume = 0;
        a.play().catch(console.warn);
        sunriseAudioRefs.current.push(a);
        gsap.to(a, {
          volume: AUDIO_CONFIG.sunrise.volumes[idx] ?? 0.35,
          duration: AUDIO_CONFIG.fadeInDuration / 1000,
          ease: 'power1.inOut',
          overwrite: true
        });
      });
      console.log('🌅 Transition audio: nuit → lever (roosters + blackbird)');
    };

    if (total === 0) {
      stopAndStart();
      return;
    }

    nightAudioRefs.current.forEach((audio) => {
      try {
        gsap.to(audio, {
          volume: 0,
          duration: AUDIO_CONFIG.fadeOutDuration / 1000,
          ease: 'power1.inOut',
          overwrite: true,
          onComplete: () => {
            completed += 1;
            if (completed >= total) {
              stopAndStart();
            }
          }
        });
      } catch {
        completed += 1;
        if (completed >= total) {
          stopAndStart();
        }
      }
    });
  }, []);

  // 🌅 ANIMATION DU SOLEIL QUI SE LÈVE (CISCO: derrière le paysage)
  // Correction CISCO : le soleil monte progressivement APRÈS le coucher de la lune
  const sunAnimationStarted = useRef(false);
  const animateSunrise = (progressIntensity: number) => {
    if (!sunRef.current) return;
    let effectiveIntensity = Math.max(0, Math.min(progressIntensity, 1));
    const sunStartY = 250;
    const sunEndY = 25;
    let sunSize = 90;
    // Nouvelle logique de halo : lever chaud, transition vers blanc/beige
    // 🔧 CISCO: Halo simple
    let sunHalo;
    if (effectiveIntensity < 0.15) {
      // Soleil juste au-dessus du paysage : halo chaud, contours lumineux, pas de gris
      sunHalo = `radial-gradient(circle, rgba(255,255,245,0.95) 0%, #FFD1A1 25%, #FFB07A 55%, rgba(255,180,120,0.25) 80%, rgba(255,255,255,0.01) 100%)`;
      sunSize = 75;
    } else if (effectiveIntensity < 0.35) {
      // Phase chaude accentuée, éclat renforcé, transition plus lente
      sunHalo = `radial-gradient(circle, rgba(255,255,250,0.98) 0%, #FFE5C2 30%, #FFD1A1 60%, rgba(255,200,150,0.18) 85%, rgba(255,255,255,0.01) 100%)`;
      sunSize = 90;
    } else if (effectiveIntensity < 0.7) {
      // Transition douce vers blanc/beige, contours lumineux
      sunHalo = `radial-gradient(circle, rgba(255,255,255,1) 0%, #FFFDF5 45%, #FFE9B0 75%, rgba(255,255,255,0.08) 95%, rgba(255,255,255,0.01) 100%)`;
      sunSize = 105;
    } else {
      // Soleil au zénith : halo très éclatant, centre blanc pur, contours transparents
      sunHalo = `radial-gradient(circle, rgba(255,255,255,1) 0%, #FFFDF5 60%, rgba(255,255,255,0.01) 100%)`;
      sunSize = 120;
    }
    // 🔧 CISCO: SIMPLE - Le soleil apparaît dès le début (pas de délai)
    if (effectiveIntensity < 0.1) {
      gsap.set(sunRef.current, {
        y: `${sunStartY}%`,
        opacity: 0,
        width: `${sunSize}px`,
        height: `${sunSize}px`,
        background: sunHalo
      });
      sunAnimationStarted.current = false;
      return;
    }

    // Démarre l'animation de montée une seule fois après le délai
    if (!sunAnimationStarted.current) {
      gsap.to(sunRef.current, {
        y: `${sunEndY}%`,
        opacity: 1,
        width: `${sunSize}px`,
        height: `${sunSize}px`,
        background: sunHalo,
        duration: 50.0, // Encore plus lent
        ease: "power1.inOut"
      });
      sunAnimationStarted.current = true;
    } else {
      // Mise à jour de la couleur et taille du halo pendant la progression
      sunRef.current.style.background = sunHalo;
      sunRef.current.style.width = `${sunSize}px`;
      sunRef.current.style.height = `${sunSize}px`;
    }
  };

  // 🌙 ANIMATION DE LA LUNE QUI SE COUCHE - MODE INDÉPENDANT
  const animateMoonset = (moonProgressIntensity: number) => {
    if (!moonRef.current) return;

    // 🌙 CISCO: MOUVEMENT INDÉPENDANT DE LA LUNE - TRAJECTOIRE COMPLÈTE
    // La lune suit sa propre trajectoire, complètement découplée du soleil

    // 📍 POSITIONS VERTICALES (Y) - De haut en bas
    const moonStartY = 120;  // DÉPART : Position haute (visible dans le ciel)
    const moonEndY = 1200;   // ARRIVÉE : Position finale (TRÈS bas, complètement hors écran)

    // 🧮 CALCUL POSITION Y ACTUELLE
    let currentMoonY;
    if (moonProgressIntensity >= 1.0) {
      currentMoonY = moonEndY; // Position finale garantie
    } else {
      // Interpolation linéaire : 120% → 1200% selon progression (0 → 1)
      currentMoonY = moonStartY + (moonProgressIntensity * (moonEndY - moonStartY));
    }

    // 📍 POSITIONS HORIZONTALES (X) - Trajectoire en arc
    const moonStartLeft = 10;   // DÉPART : Commence à gauche (10%)
    const moonEndLeft = 150;    // ARRIVÉE : Finit plus à droite (150%)

    // 🧮 CALCUL POSITION X ACTUELLE (CORRIGÉ)
    const currentMoonLeft = moonStartLeft + (moonProgressIntensity * (moonEndLeft - moonStartLeft));

    // 👁️ OPACITÉ - La lune reste visible pendant tout son parcours
    const moonOpacity = 1.0;

    // 🎬 ANIMATION GSAP ULTRA-FLUIDE - Paramètres détaillés
    gsap.to(moonRef.current, {
      y: `${currentMoonY}%`,        // POSITION Y : de 120% à 1200% (descente)
      left: `${currentMoonLeft}%`,  // POSITION X : de 10% à 150% (arc horizontal)
      opacity: moonOpacity,         // OPACITÉ : toujours visible (1.0)
      width: '140px',               // TAILLE : constante 140px
      height: '140px',              // TAILLE : constante 140px
      duration: 0.1,                // DURÉE : 0.1s transition douce entre positions
      ease: "none",                 // COURBE : linéaire (pas d'accélération/décélération)
      overwrite: true               // SÉCURITÉ : évite les conflits d'animation
    });

    // 🔍 CISCO: Debug position actuelle (seulement tous les 10%)
    if (Math.floor(moonProgressIntensity * 10) !== Math.floor((moonProgressIntensity - 0.01) * 10)) {
      console.log(`🌙 Position: Y=${currentMoonY.toFixed(1)}% X=${currentMoonLeft.toFixed(1)}% Progress=${(moonProgressIntensity*100).toFixed(1)}%`);
    }
  };

  // ⭐ ANIMATION DES ÉTOILES QUI DISPARAISSENT (CISCO: progressif naturel)
  const animateStarsFading = (progressIntensity: number) => {
    if (!starsContainerRef.current) return;
    // Disparition encore plus progressive et lente
    let starsOpacity;
    if (progressIntensity < 0.2) {
      starsOpacity = 1.0;
    } else if (progressIntensity < 0.85) {
      // Disparition très étalée sur la fin
      starsOpacity = 1.0 - ((progressIntensity - 0.2) / 0.65);
    } else {
      starsOpacity = 0;
    }
    gsap.to(starsContainerRef.current, {
      opacity: Math.max(starsOpacity, 0),
      duration: 6.0, // Encore plus lent pour effet naturel
      ease: "power2.out"
    });
  };

  // 💡 ÉCLAIRAGE GLOBAL SIMPLE (CISCO: directement lié à l'intensité)
  const animateGlobalLighting = (progressIntensity: number) => {
    if (!globalLightRef.current) return;

    // 🔧 CISCO: SIMPLE - Éclairage suit directement l'intensité
    const lightIntensity = Math.max(0, Math.min(progressIntensity, 1));

    gsap.to(globalLightRef.current, {
      opacity: lightIntensity,
      duration: 1.0, // Plus rapide pour réactivité
      ease: "power1.inOut"
    });

    console.log(`💡 CISCO: Éclairage global - Intensité: ${lightIntensity.toFixed(3)}`);
  };

  // 🏔️ ÉCLAIRAGE DU PAYSAGE (CISCO: s'éclaircit progressivement)
  const animateLandscapeLighting = (progressIntensity: number) => {
    if (!landscapeRef.current) return;

    // CISCO: Le paysage s'éclaircit avec le lever du soleil
    let brightness;
    if (progressIntensity < 0.1) {
      brightness = 0.15; // Très sombre au début (nuit)
    } else if (progressIntensity < 0.7) {
      brightness = 0.15 + (progressIntensity - 0.1) * 1.0; // Éclaircissement progressif
    } else {
      brightness = 0.75 + (progressIntensity - 0.7) * 0.5; // Pleine lumière
    }

    gsap.to(landscapeRef.current, {
      filter: `brightness(${brightness})`,
      duration: 3.5,
      ease: "power1.inOut"
    });
  };

  // 🎨 CISCO: DÉGRADÉ ULTRA-SIMPLE ET PROGRESSIF
  const createSunriseGradient = (progressIntensity: number) => {
    if (!containerRef.current) return;

    // 🔧 CISCO: INTERPOLATION SIMPLE ENTRE 2 COULEURS
    const intensity = Math.max(0, Math.min(progressIntensity, 1));

    // Interpoler entre bleu nuit et bleu jour
    const nightColor = SIMPLE_COLORS.NIGHT_START; // '#001540'
    const dayColor = SIMPLE_COLORS.DAY_END;       // '#87CEEB'

    // Calculer la couleur intermédiaire
    const r1 = parseInt(nightColor.slice(1, 3), 16);
    const g1 = parseInt(nightColor.slice(3, 5), 16);
    const b1 = parseInt(nightColor.slice(5, 7), 16);

    const r2 = parseInt(dayColor.slice(1, 3), 16);
    const g2 = parseInt(dayColor.slice(3, 5), 16);
    const b2 = parseInt(dayColor.slice(5, 7), 16);

    const r = Math.round(r1 + (r2 - r1) * intensity);
    const g = Math.round(g1 + (g2 - g1) * intensity);
    const b = Math.round(b1 + (b2 - b1) * intensity);

    const currentColor = `rgb(${r}, ${g}, ${b})`;

    // Dégradé simple du bas vers le haut
    const gradient = `linear-gradient(to top, ${currentColor} 0%, ${currentColor} 100%)`;

    console.log(`🎨 CISCO SIMPLE: Intensité ${(intensity * 100).toFixed(0)}% → Couleur ${currentColor}`);

    // Application directe
    containerRef.current.style.backgroundImage = gradient;
  };

  // 🎬 ORCHESTRATION COMPLÈTE DU LEVER DE SOLEIL (CISCO: tout synchronisé)
  const orchestrateSunrise = (progressIntensity: number) => {
    console.log(`🌅 CISCO Orchestration lever de soleil - Intensité: ${progressIntensity.toFixed(2)} - Phase: ${currentPhase}`);

    // Tuer l'animation précédente si elle existe
    if (timelineRef.current) {
      timelineRef.current.kill();
    }

    // Créer une nouvelle timeline synchronisée
    timelineRef.current = gsap.timeline();

    // CISCO: Lancer toutes les animations en parallèle - TOUT synchronisé
  createSunriseGradient(progressIntensity);
  animateSunrise(progressIntensity);
  // 🌙 CISCO: La lune est animée INDÉPENDAMMENT avec sa propre intensité
  // Note: animateMoonset est appelée séparément dans useEffect avec moonIntensity
  animateStarsFading(progressIntensity);
  animateGlobalLighting(progressIntensity);
  animateLandscapeLighting(progressIntensity);

  // CISCO: Gestion des phases (audio géré ailleurs)
    if (progressIntensity < 0.1 && currentPhase !== 'nuit') {
      setCurrentPhase('nuit');
    } else if (progressIntensity >= 0.1 && progressIntensity < 0.6 && currentPhase !== 'transition') {
      const newPhase = 'transition';
      setCurrentPhase(newPhase);
    } else if (progressIntensity >= 0.6 && currentPhase !== 'lever') {
      setCurrentPhase('lever');
    }
  };

  // 🔧 CISCO: PLUS BESOIN DE TEMPORISATEUR COMPLEXE
  // La progression se fait naturellement via currentIntensity qui évolue

  // 🎯 CISCO: Méthodes publiques pour contrôle externe
  const startProgression = () => {
    if (!isProgressing) {
      console.log('🌅 CISCO: Démarrage progression simple');
      setIsProgressing(true);
      setCurrentIntensity(0.0);

      if (onProgressionStart) {
        onProgressionStart();
      }
    }
  };

  const stopProgression = () => {
    console.log('🌅 CISCO: Arrêt de la progression du lever de soleil');
    setIsProgressing(false);

  // Arrêter l'audio
  nightAudioRefs.current.forEach(a => { try { a.pause(); } catch {} });
  sunriseAudioRefs.current.forEach(a => { try { a.pause(); } catch {} });
  nightAudioRefs.current = [];
  sunriseAudioRefs.current = [];
  sunriseStartedRef.current = false;

    // Remettre en mode nuit
    setCurrentIntensity(0.0);
    setCurrentPhase('nuit');
    orchestrateSunrise(0.0);
  };

  const getCurrentPhase = () => {
    if (currentIntensity < 0.25) return 'Nuit profonde';
    if (currentIntensity < 0.5) return 'Fin de nuit';
    if (currentIntensity < 0.75) return 'Aube';
    return 'Lever de soleil';
  };

  // 🔧 CISCO: Exposer les méthodes pour contrôle externe via window
  React.useEffect(() => {
    if (typeof window !== 'undefined') {
      (window as any).sunriseControls = {
        start: startProgression,
        stop: stopProgression,
        getPhase: getCurrentPhase,
        getIntensity: () => currentIntensity
      };
    }
  }, [currentIntensity, isProgressing]);

  // 🔄 EFFET PRINCIPAL - RÉACTION AUX CHANGEMENTS (CISCO: démarrage en mode nuit)
  useEffect(() => {
    if (isActive) {
      console.log('🌅 CISCO: ACTIVATION MODULE LEVER DE SOLEIL - Démarrage nuit complète');
  // La lune repart pour une nouvelle course
  setIsMoonFinished(false);

      // 🔧 CISCO: FORCER IMMÉDIATEMENT LE MODE NUIT - Éviter le flash
      // Le dégradé sera appliqué par orchestrateSunrise(0.0) juste après

      // CISCO: TOUJOURS démarrer en mode nuit (intensité 0)
      orchestrateSunrise(0.0); // Force le mode nuit au démarrage
  // Démarrer l'ambiance sonore de nuit
  playNightSound();

      // Synchronisation : le soleil démarre UNIQUEMENT après la lune
      // (moonIntensity >= 1.0)
      // On utilise un effet séparé pour surveiller la lune
    } else {
      console.log('🌅 CISCO: DÉSACTIVATION MODULE LEVER DE SOLEIL');

      // Arrêter la progression automatique
      setIsProgressing(false);

      // 🔧 CISCO: RÉINITIALISATION COMPLÈTE DES ÉTATS
      setCurrentIntensity(0.0);
      setMoonIntensity(0.0);
      setIsMoonFinished(false);
      setCurrentPhase('nuit');

  // Arrêter l'audio
  nightAudioRefs.current.forEach(a => { try { a.pause(); } catch {} });
  sunriseAudioRefs.current.forEach(a => { try { a.pause(); } catch {} });
  nightAudioRefs.current = [];
  sunriseAudioRefs.current = [];
  sunriseStartedRef.current = false;

      // 🔧 CISCO: Nettoyer l'intervalle de la lune si actif
      if (moonIntervalRef.current) {
        clearInterval(moonIntervalRef.current);
        moonIntervalRef.current = null;
      }

      // 🔧 CISCO: SUPPRESSION FADE OUT - Application immédiate pour éviter dégradé
      if (containerRef.current) {
        containerRef.current.style.opacity = '0';
      }
    }

    return () => {
      if (timelineRef.current) {
        timelineRef.current.kill();
      }
  nightAudioRefs.current.forEach(a => { try { a.pause(); } catch {} });
  sunriseAudioRefs.current.forEach(a => { try { a.pause(); } catch {} });
    };
  }, [isActive, autoStart]); // Suppression de 'intensity' pour éviter les re-renders

  // 🔧 CISCO: SUPPRESSION DÉCLENCHEUR AUTOMATIQUE - ARRÊTER LES CONFLITS

  // 🔧 CISCO: SUPPRESSION EFFET PROGRESSION - ARRÊTER LA BOÎTE DE NUIT

  // 🔄 EFFET POUR LA PROGRESSION INDÉPENDANTE DE LA LUNE
  useEffect(() => {
    // 🚨 CISCO: LUNE COMPLÈTEMENT INDÉPENDANTE - ne dépend QUE de isActive
    if (isActive && !moonIntervalRef.current) {
      // Vitesse lune : accélérée (40s)
      // 🌙 CISCO: CONTRÔLE DE VITESSE INDÉPENDANT DE LA LUNE
      const moonSpeedFactor = 60; // VITESSE : 60s POUR TEST SIMPLE
      const moonDuration = moonSpeedFactor * 1000; // DURÉE TOTALE : convertit en millisecondes (400s = 400000ms)
      const updateInterval = 50; // FRÉQUENCE : mise à jour toutes les 50ms pour fluidité maximale
      const incrementPerUpdate = 1.0 / (moonDuration / updateInterval); // PROGRESSION : petit pas à chaque update (1/8000 = 0.000125 par update)
  // 🌙 CISCO: Réinitialisation SEULEMENT au début d'une nouvelle progression
  setMoonIntensity(0.0);
  setIsMoonFinished(false);
      console.log(`🌙 CISCO: Démarrage mouvement indépendant de la lune (${moonSpeedFactor}s)`);

      moonIntervalRef.current = setInterval(() => {
        setMoonIntensity(prev => {
          const newIntensity = prev + incrementPerUpdate;
          // 🔍 CISCO: Debug - affiche la progression toutes les 10%
          if (Math.floor(newIntensity * 10) !== Math.floor(prev * 10)) {
            console.log(`🌙 DIAGNOSTIC: ${(newIntensity * 100).toFixed(1)}% - Y: ${120 + (newIntensity * (1200 - 120))}% - Interval actif: ${moonIntervalRef.current ? 'OUI' : 'NON'}`);
          }
          if (newIntensity >= 1.0) {
            if (moonIntervalRef.current) {
              clearInterval(moonIntervalRef.current);
              moonIntervalRef.current = null;
            }
            console.log('🌙 CISCO: Lune terminée - position finale atteinte');
            console.log('🔄 CISCO: Déclenchement setIsMoonFinished(true) pour synchronisation');
            setIsMoonFinished(true);
            return 1.0;
          }
          return newIntensity;
        });
      }, updateInterval);
      return () => {
        if (moonIntervalRef.current) {
          clearInterval(moonIntervalRef.current);
          moonIntervalRef.current = null;
        }
      };
    }
    // 🌙 CISCO: PAS de réinitialisation automatique - la lune reste en position finale
  }, [isActive]); // 🌙 CISCO: Dépend SEULEMENT de isActive, pas de isProgressing

  // 🌙 CISCO: SYSTÈME SIMPLE - La lune pilote TOUT le reste
  useEffect(() => {
    // 1. Animation de la lune (position)
    animateMoonset(moonIntensity);

    // 2. CISCO: La lune pilote TOUT dès le début !
    // Lune 0% → Soleil 0% (nuit profonde)
    // Lune 100% → Soleil 100% (jour complet)
    const sunIntensity = Math.max(0, Math.min(moonIntensity, 1));

    // 🎨 Dégradé change avec la lune DÈS LE DÉBUT
    createSunriseGradient(sunIntensity);

    // ☀️ Soleil se lève avec la lune DÈS LE DÉBUT
    animateSunrise(sunIntensity);

    // 💡 Éclairage global suit la lune DÈS LE DÉBUT
    animateGlobalLighting(sunIntensity);

    // 🌄 Paysage s'éclaire avec la lune DÈS LE DÉBUT
    animateLandscapeLighting(sunIntensity);

    console.log(`🌙 CISCO SIMPLE: Lune ${(moonIntensity * 100).toFixed(0)}% → Soleil ${(sunIntensity * 100).toFixed(0)}%`);
  }, [moonIntensity]);

  // 🔧 CISCO: GROS NETTOYAGE TERMINÉ - SYSTÈME ULTRA-SIMPLE

  // 🌟 CISCO: ÉTOILES GÉRÉES PAR SimpleStars.tsx

  // Mini-box modale d'infos dev (toujours visible en haut à gauche)

  const elapsedSeconds = Math.floor(currentIntensity * effectiveTimerDuration);
  let degradeMode = 'Nuit';
  let phaseName = 'Première phase : Nuit';
  let phaseTimes = '0s - 15s';
  if (currentIntensity < 0.25) {
    degradeMode = 'Nuit';
    phaseName = 'Première phase : Nuit';
    phaseTimes = '0s - 15s';
  } else if (currentIntensity < 0.5) {
    degradeMode = 'Transition';
    phaseName = 'Deuxième phase : Transition';
    phaseTimes = '15s - 30s';
  } else if (currentIntensity < 0.85) {
    degradeMode = 'Aube';
    phaseName = 'Troisième phase : Aube';
    phaseTimes = '30s - 51s';
  } else {
    degradeMode = 'Lever du soleil';
    phaseName = 'Quatrième phase : Lever du soleil';
    phaseTimes = '51s - 60s';
  }

  // Éclairage (simulé) - Utiliser la prop intensity si fournie, sinon l'état interne
  const effectiveIntensity = intensity !== undefined ? intensity : currentIntensity;
  const nuagesLight = degradeMode === 'Nuit' ? 'très sombre' : degradeMode === 'Transition' ? 'bleu nuit' : degradeMode === 'Aube' ? 'rosé/doré' : 'jaune/blanc';
  const globalLight = (effectiveIntensity * 100 < 20) ? 'très faible' : (effectiveIntensity * 100 < 50) ? 'progressif' : (effectiveIntensity * 100 < 85) ? 'lumineux' : 'très lumineux';
  const paysageLight = degradeMode === 'Nuit' ? 'sombre' : degradeMode === 'Transition' ? 'bleu-gris' : degradeMode === 'Aube' ? 'doré' : 'clair';

  // Progression en %
  const progressionPercent = Math.round(effectiveIntensity * 100);
  // Soleil : position Y (simulée)
  const sunY = 250 - (225 * effectiveIntensity);
  // Lune : position Y (simulée)
  const moonY = 30 + (130 * effectiveIntensity);
  // Opacité étoiles
  const starsOpacity = effectiveIntensity < 0.6 ? (1 - effectiveIntensity * 1.5) : 0;

  return (
    <>
      {showDevModal && (
        <div style={{position:'fixed',top:16,left:16,zIndex:100000,background:'#222d',color:'#fff',padding:'12px 18px',borderRadius:'12px',fontSize:'1em',boxShadow:'0 2px 12px #0008',minWidth:260}}>
          <div style={{display:'flex',justifyContent:'space-between',alignItems:'center',marginBottom:4}}>
            <div style={{fontWeight:'bold'}}>🛠️ Infos Animation Lever de Soleil</div>
            <button 
              onClick={onCloseDevModal}
              style={{background:'none',border:'none',color:'#fff',cursor:'pointer',fontSize:'16px',padding:'0 4px'}}
            >
              ✕
            </button>
          </div>
          <div><b>Mode dégradé :</b> {degradeMode}</div>
          <div><b>Phase active :</b> {phaseName} ({phaseTimes})</div>
          <div><b>Éclairage nuages :</b> {nuagesLight}</div>
          <div><b>Éclairage global :</b> {globalLight}</div>
          <div><b>Éclairage paysage :</b> {paysageLight}</div>
          <div><b>Temps écoulé :</b> {elapsedSeconds}s</div>
          <div><b>Phase :</b> {currentPhase}</div>
          <div><b>Progression :</b> {isProgressing ? 'OUI' : 'NON'} ({progressionPercent}%)</div>
          <div><b>Soleil Y :</b> {Math.round(sunY)}%</div>
          <div><b>Lune Y :</b> {Math.round(moonY)}%</div>
          <div><b>Opacité étoiles :</b> {starsOpacity.toFixed(2)}</div>
        </div>
      )}
      <div
        ref={containerRef}
        className="fixed inset-0 pointer-events-none"
        style={{
          zIndex: 5,
          opacity: isActive ? 1 : 0
          // 🔧 CISCO: backgroundImage retiré - géré dynamiquement par createSunriseGradient
        }}
      >
      {/* 🌙 LUNE AVEC HALO DISCRET (CISCO: descend très bas, mouvement indépendant) */}
      <img
        ref={moonRef}
        src="/Lune-Moon.png"
        alt="Lune"
        className="absolute"
        style={{
          width: '140px',
          height: '140px',
          objectFit: 'contain',
          opacity: 1,
          zIndex: 2,
          pointerEvents: 'none',
          userSelect: 'none',
          transition: 'width 0.3s, height 0.3s',
          // 🌙 CISCO: Halo lunaire plus grand pour impression d'éclairage
          filter: 'drop-shadow(0 0 35px rgba(255, 255, 255, 0.5)) drop-shadow(0 0 70px rgba(220, 220, 255, 0.3)) drop-shadow(0 0 120px rgba(200, 200, 255, 0.15))',
          borderRadius: '50%'
        }}
      />

      {/* 🌅 SOLEIL LEVANT (CISCO: derrière le paysage) */}
      <div
        ref={sunRef}
        className="absolute"
        style={{
    left: '75%',
    top: '50%',
    transform: 'translate(-50%, -50%)',
    width: '90px',
    height: '90px',
          borderRadius: '50%',
          background: `radial-gradient(circle, rgba(255,255,255,0.95) 0%, #FFFDF5 60%, rgba(255,255,255,0.01) 100%)`,
          boxShadow: `0 0 120px 60px #FFFDF5, 0 0 200px 80px #FFFDF5, 0 0 40px 10px #FFFDF5`,
          opacity: 1,
    zIndex: 3,
          pointerEvents: 'none',
          userSelect: 'none'
        }}
      />

      {/* ⭐ ÉTOILES UNIFIÉES - SYSTÈME COMPLET (CISCO: 300+ étoiles) */}
      <UnifiedStars
        skyMode="leverSoleil"
        isVisible={isActive}
        opacity={effectiveIntensity < 0.6 ? (1 - effectiveIntensity * 1.5) : 0}
      />

      {/* 💡 ÉCLAIRAGE GLOBAL PROGRESSIF (CISCO: synchronisé avec le soleil) */}
      <div
        ref={globalLightRef}
        className="absolute inset-0"
        style={{
          background: `radial-gradient(ellipse at 75% 60%,
            #FFE9B015 0%,
            #FFF8DC08 30%,
            #FFFDF505 50%,
            transparent 75%)`,
          opacity: 0,
          zIndex: 6
        }}
      />

      {/* 🏔️ RÉFÉRENCE PAYSAGE POUR ÉCLAIRAGE (CISCO: s'éclaircit progressivement) */}
      <div
        ref={landscapeRef}
        className="absolute inset-0"
        style={{
          background: 'transparent',
          filter: 'brightness(0.15)', // Démarrage très sombre
          zIndex: 7
        }}
      />

      </div>
    </>
  );
};

export default ModeLeverSoleil;
